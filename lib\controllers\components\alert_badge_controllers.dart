import 'dart:convert';

import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';

class AlertBadgeControllers extends BaseControllers {
  RxInt count = 0.obs;

  @override
  void onInit() {
    super.onInit();
    getCountUnreadInbox();
  }

  getCountUnreadInbox() async {
    print('here dapat get brouw');
    await api.getCountUnreadInbox(controllers: this, code: 0);
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    parseData(response);
  }

  parseData(response) {
    final List<dynamic> jsonList = json.decode(response);

    final List<NotificationItem> items =
        jsonList.map((json) => NotificationItem.fromJson(json)).toList();

    final int totalCount = items.fold(0, (sum, item) => sum + item.count);

    print("here Total Count: $totalCount"); // Output: 23
  }
}

class NotificationItem {
  final String trxType;
  final int count;

  NotificationItem({required this.trxType, required this.count});

  factory NotificationItem.fromJson(Map<String, dynamic> json) {
    return NotificationItem(
      trxType: json['trxType'] ?? '',
      count: json['count'] ?? 0,
    );
  }
}
